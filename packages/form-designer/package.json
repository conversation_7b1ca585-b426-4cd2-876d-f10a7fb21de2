{"name": "form-designer", "private": true, "version": "1.0.0", "type": "module", "description": "高性能Vue低代码可视化表单设计器，通过拖拽快速创建表单，提升开发效率。", "main": "./dist/index.umd.js", "module": "./dist/index.es.js", "typings": "./types/index.d.ts", "scripts": {"dev": "vite", "build": "vite build --config ./vite.config.build.js", "build:lib": "vite build --config ./vite.config.build.js", "preview": "vite preview"}, "dependencies": {"@form-create/component-wangeditor": "^3.2.14", "@form-create/element-ui": "^3.2.27", "@form-create/utils": "^3.2.0", "codemirror": "^6.65.7", "element-plus": "^2.11.1", "js-beautify": "^1.15.1", "signature_pad": "^5.0.10", "vue": "^3.5.18", "vue-element-plus-x": "^1.3.7", "vuedraggable": "4.1.0"}, "devDependencies": {"@babel/core": "^7.28.3", "@babel/preset-env": "^7.28.3", "@element-plus/icons-vue": "^0.2.6", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^2.0.1", "@vue/babel-plugin-jsx": "^1.0.7", "cssnano": "^5.1.13", "rimraf": "^3.0.2", "vite": "^7.1.2", "vite-plugin-banner": "^0.8.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "peerDependencies": {"vue": "^3.5"}}